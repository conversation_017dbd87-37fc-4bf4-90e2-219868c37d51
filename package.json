{"name": "three-tools", "private": true, "version": "0.0.0", "type": "module", "main": "./dist/three-tool.es.js", "module": "./dist/three-tool.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/three-tool.es.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vite build && tsc -p tsconfig.build.json", "build:types": "tsc -p tsconfig.build.json", "build:js": "vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^24.0.10", "typescript": "~5.8.3", "vite": "^7.0.0"}}