{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    /* Emit */
    "noEmit": false,
    "emitDeclarationOnly": true,
    "declaration": true,
    "declarationMap": true,
    "outDir": "./dist",
    "declarationDir": "./dist",

    /* 禁用与 Vite 冲突的选项 */
    "allowImportingTsExtensions": false,
    "verbatimModuleSyntax": false
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "vite.config.ts"
  ]
}
